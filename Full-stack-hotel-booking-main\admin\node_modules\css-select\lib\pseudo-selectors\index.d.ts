import type { CompiledQuery, InternalOptions, CompileToken } from "../types";
import { PseudoSelector } from "css-what";
import { filters } from "./filters";
import { pseudos } from "./pseudos";
import { aliases } from "./aliases";
export { filters, pseudos, aliases };
export declare function compilePseudoSelector<Node, ElementNode extends Node>(next: CompiledQuery<ElementNode>, selector: PseudoSelector, options: InternalOptions<Node, ElementNode>, context: Node[] | undefined, compileToken: CompileToken<Node, ElementNode>): CompiledQuery<ElementNode>;
//# sourceMappingURL=index.d.ts.map