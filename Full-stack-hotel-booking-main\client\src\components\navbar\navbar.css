.navbar{
    height: 50px;
    width: 100%;
    background-color: #003580;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .navContainer{
    width: 50%;
    max-width: 1024px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    
  }
  
  .profile{
    /* color: rgb(0, 0, 0); */
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 10px;
    text-transform: capitalize;
    width: 200px;
  }

.name {
  display: flex;
  width: 50%;
  color: aliceblue;
}

  .logo{
    font-weight: 500;
  }
  .navItems {
    display: flex;
  }

  .navButton{
    margin-left: 20px;
    border: none;
    padding: 5px 15px;
    cursor: pointer;
    color: #0f1010;
    border-radius: 3px;
    display: flex;
    place-items: center;
    gap: 3px;
  }
