.fp {
    width: 100%;
    max-width: 1024px;
    display: flex;
    justify-content: space-between;
    gap: 20px;
  }
  
  .fpItem{
    flex: 1;
    gap: 10px;
    display: flex;
    flex-direction: column;
  }
  
  .fpImg{
    width: 100%;
    height: 250px;
    object-fit: cover;
  }
  
  .fpName{
    color: #333;
    font-weight: bold;
  }
  
  .fpCity{
    font-weight: 300;
  }
  
  .fpPrice{
    font-weight: 500;
  }
  
  .fpRating {
    display: flex;
    gap: 10px;
    height: max-content;
 }
  
  .whole {
    display: flex;
    gap: 2px;
  }

  .star {
    color: gold;
  }

  .fpRating>span{
    font-size: 14px;
  }