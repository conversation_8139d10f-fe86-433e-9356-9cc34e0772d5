.Modal {
    height: max-content;
    position: fixed;
    place-items: center;
    top: 20%;
    left: 40%;
    z-index: 4;
    color: #fff;
}

.closer {
    position: absolute;
    font-size: 20px;
    top: 10px;
    right: 10px;
    cursor: pointer;
}

.closer:hover {
    color: rgb(211, 204, 204);
}

.ModalContainer{
    border-radius: 5px;
    padding: 40px;
    border: 1px solid black;
    background-color: #19488a;
    display: flex;
    flex-direction: column;
    gap: 20px;
    text-transform: capitalize;

}

p{
    text-align: center;
    font-weight: 600;
    margin-bottom: 40px;
}

.ModalBtn{
    padding: 9px 14px;
    cursor: pointer;
    background-color: #1f84d6;
    border: none;
    outline: none;
    border-radius: 3px;
    color: #fff;
    text-align: center;
}

select {
    padding: 5px 11px;
    margin:0px 10px;
}

.roomContents {
    display: flex;
    flex-direction: column;
    gap: 9px;
}

.btnDiv{
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.roomSelector {
    display: flex;
    place-items: center;
    flex-direction: column;
    width: max-content;
}

.roomNumber{
    width: 100%;
    display: flex;
    gap: 10px;
    flex-direction: row;
}

input[type='checkbox']{
    width: 20px;
    cursor: pointer;
}