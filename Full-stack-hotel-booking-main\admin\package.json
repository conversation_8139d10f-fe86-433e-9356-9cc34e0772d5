{"name": "admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@cloudinary/react": "^1.13.0", "@cloudinary/url-gen": "^1.21.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mui/icons-material": "^6.1.4", "@mui/material": "^6.1.4", "@mui/x-data-grid": "^7.21.0", "@testing-library/jest-dom": "^6.6.2", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "axios": "^1.7.7", "cloudinary": "^2.5.1", "dotenv": "^16.4.5", "firebase": "^11.0.1", "react": "^18.3.1", "react-circular-progressbar": "^2.1.0", "react-dom": "^18.3.1", "react-router-dom": "^6.27.0", "react-scripts": "^5.0.1", "recharts": "^2.13.0", "uuid": "^10.0.0", "web-vitals": "^4.2.3"}, "devDependencies": {"@eslint/js": "^9.11.1", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.2", "eslint": "^9.11.1", "eslint-plugin-react": "^7.37.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "sass": "^1.80.3", "vite": "^5.4.8"}, "proxy": "http://localhost:8001/api"}