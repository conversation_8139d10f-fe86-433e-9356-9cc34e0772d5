{"name": "api", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cloudinary": "^2.5.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.8.0", "mongoose": "^8.5.1", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.1.4"}}