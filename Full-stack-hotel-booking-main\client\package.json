{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome": "^1.1.8", "@fortawesome/free-solid-svg-icons": "^6.6.0", "axios": "^1.7.5", "date-fns": "^3.6.0", "react": "^18.3.1", "react-date-range": "^2.0.1", "react-dom": "^18.3.1", "react-fontawesome": "^1.7.1", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0", "react-router-dom": "^6.26.1"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "vite": "^5.4.1"}}