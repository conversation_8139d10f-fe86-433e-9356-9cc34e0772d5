export const userInputs = [
    {
      id: 'username',
      label: "<PERSON>rname",
      type: "text",
      placeholder: "john_doe",
    },
    {
      id: 'email',
      label: "Email",
      type: "email",
      placeholder: "<PERSON><PERSON><PERSON><PERSON>@gmail.com",
    },
    {
      id: 'phone',
      label: "Phone",
      type: "text",
      placeholder: "****** 567 89",
    },
    {
      id: 'password',
      label: "Password",
      type: "password",
    },
    {
      id: 'country',
      label: "Country",
      type: "text",
      placeholder: "United states",
    },
    {
      id: 'city',
      label: "City",
      type: "text",
      placeholder: "Golden streets",
    },
  ];
  
  export const hotelInputs = [
    {
      id: 'name',
      label: "Name",
      type: "text",
      placeholder: "my hotel",
    },
    {
      id: 'type',
      label: "Type",
      type: "text",
      placeholder: "hotel type",
    },
    {
      id: 'city',
      label: "City",
      type: "text",
      placeholder: "new york",
    },
    {
      id: 'address',
      label: "Address",
      type: "text",
      placeholder: "Peter street",
    },
    {
      id: 'distance',
      label: "Distance from center",
      type: "text",
      placeholder: "500m",
    },
    {
      id: 'title',
      label: "Title",
      type: "text",
      placeholder: "Five start hotel",
    },
    {
      id: 'desc',
      label: "Descriptiom",
      type: "text",
      placeholder: "hotel description",
    },
    {
      id: 'cheapestPrice',
      label: "hotel price",
      type: "text",
      placeholder: "210",
    },
  ];

  export const roomInputs = [{
    id: 'title',
    label: "Title",
    type: "text",
    placeholder: "my room",
  },{
    id: 'desc',
    label: "Description",
    type: "text",
    placeholder: "good looking room",
  },{
    id: 'price',
    label: "Price",
    type: "text",
    placeholder: "100",
  },{
    id: 'maxpeople',
    label: "maximum-people",
    type: "text",
    placeholder: "2",
  },]